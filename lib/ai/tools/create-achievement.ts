import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { createAchievement } from '@/lib/db/queries';

interface CreateAchievementProps {
  session: Session;
}

export const createAchievementTool = ({ session }: CreateAchievementProps) =>
  tool({
    description: 'Create a new achievement record for the current authenticated user. Accepts both processed achievement text and raw unprocessed text.',
    inputSchema: z.object({
      achievement: z.string().min(1).max(2000).describe('The processed achievement text'),
      achievementUnprocessed: z.string().min(1).max(2000).describe('The raw, unprocessed achievement data'),
    }),
    execute: async ({ achievement, achievementUnprocessed }) => {
      console.log('Creating achievement', achievement, achievementUnprocessed);

      try {
        await createAchievement({
          achievement,
          achievementUnprocessed,
          userId: session.user.id,
        });

        return {
          success: true,
          message: 'Achievement created successfully',
          achievement: {
            achievement,
            achievementUnprocessed,
            userId: session.user.id,
          },
        };
      } catch (error) {
        console.error(error);
        return {
          success: false,
          error: 'Failed to create achievement',
        };
      }
    },
  });
