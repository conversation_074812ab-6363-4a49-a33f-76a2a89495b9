import { tool } from 'ai';
import { z } from 'zod';
import type { Session } from 'next-auth';
import { getAchievementById, deleteAchievementById } from '@/lib/db/queries';

interface DeleteAchievementProps {
  session: Session;
}

export const deleteAchievement = ({ session }: DeleteAchievementProps) =>
  tool({
    description: 'Delete an achievement record by its ID. Only allows deleting achievements that belong to the current authenticated user.',
    inputSchema: z.object({
      id: z.number().describe('The unique ID of the achievement to delete'),
    }),
    execute: async ({ id }) => {
      try {
        // First, verify the achievement exists and belongs to the user
        const existingAchievement = await getAchievementById({ id });

        if (!existingAchievement) {
          return {
            success: false,
            error: 'Achievement not found',
          };
        }

        // Verify the achievement belongs to the current user
        if (existingAchievement.userId !== session.user.id) {
          return {
            success: false,
            error: 'You do not have permission to delete this achievement',
          };
        }

        // Delete the achievement
        const deletedAchievement = await deleteAchievementById({ id });

        return {
          success: true,
          message: 'Achievement deleted successfully',
          deletedAchievement: {
            id: deletedAchievement.id,
            achievement: deletedAchievement.achievement,
            achievementUnprocessed: deletedAchievement.achievementUnprocessed,
            createdAt: deletedAchievement.createdAt,
            userId: deletedAchievement.userId,
          },
        };
      } catch (error) {
        return {
          success: false,
          error: 'Failed to delete achievement',
        };
      }
    },
  });
