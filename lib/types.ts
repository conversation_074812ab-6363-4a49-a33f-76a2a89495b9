import { z } from 'zod';
import type { createDocument } from './ai/tools/create-document';
import type { updateDocument } from './ai/tools/update-document';
import type { requestSuggestions } from './ai/tools/request-suggestions';
import type { viewAchievements } from './ai/tools/view-achievements';
import type { createAchievementTool } from './ai/tools/create-achievement';
import type { getAchievement } from './ai/tools/get-achievement';
import type { updateAchievementTool } from './ai/tools/update-achievement';
import type { deleteAchievement } from './ai/tools/delete-achievement';
import type { InferUITool, UIMessage } from 'ai';

import type { ArtifactKind } from '@/components/artifact';
import type { Suggestion } from './db/schema';

export type DataPart = { type: 'append-message'; message: string };

export const messageMetadataSchema = z.object({
  createdAt: z.string(),
});

export type MessageMetadata = z.infer<typeof messageMetadataSchema>;

type createDocumentTool = InferUITool<ReturnType<typeof createDocument>>;
type updateDocumentTool = InferUITool<ReturnType<typeof updateDocument>>;
type requestSuggestionsTool = InferUITool<
  ReturnType<typeof requestSuggestions>
>;
type viewAchievementsTool = InferUITool<ReturnType<typeof viewAchievements>>;
type createAchievementToolType = InferUITool<ReturnType<typeof createAchievementTool>>;
type getAchievementTool = InferUITool<ReturnType<typeof getAchievement>>;
type updateAchievementToolType = InferUITool<ReturnType<typeof updateAchievementTool>>;
type deleteAchievementTool = InferUITool<ReturnType<typeof deleteAchievement>>;

export type ChatTools = {
  createDocument: createDocumentTool;
  updateDocument: updateDocumentTool;
  requestSuggestions: requestSuggestionsTool;
  viewAchievements: viewAchievementsTool;
  createAchievement: createAchievementToolType;
  getAchievement: getAchievementTool;
  updateAchievement: updateAchievementToolType;
  deleteAchievement: deleteAchievementTool;
};

export type CustomUIDataTypes = {
  textDelta: string;
  imageDelta: string;
  sheetDelta: string;
  codeDelta: string;
  suggestion: Suggestion;
  appendMessage: string;
  id: string;
  title: string;
  kind: ArtifactKind;
  clear: null;
  finish: null;
};

export type ChatMessage = UIMessage<
  MessageMetadata,
  CustomUIDataTypes,
  ChatTools
>;

export interface Attachment {
  name: string;
  url: string;
  contentType: string;
}
